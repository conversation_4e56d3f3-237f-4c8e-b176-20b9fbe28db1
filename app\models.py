from pydantic import BaseModel, Field
from typing import List, Optional


class ArticleItem(BaseModel):
    """Model for individual article item in request."""
    index: Optional[int] = Field(default=0, description="Item index")
    key: Optional[str] = Field(default="", description="Item key/ID")
    title: Optional[str] = Field(default="", description="Item title")
    abstract: Optional[str] = Field(default="", description="Item abstract")


class GenerateTagsRequest(BaseModel):
    """Request model for generate_tags endpoint."""
    items: List[ArticleItem] = Field(..., description="List of items to process")
    collection: Optional[str] = Field(default="default_collection", description="Collection to use for matching")
    model: Optional[str] = Field(default="gpt-4o-mini", description="Model to use for processing")


class TagsResult(BaseModel):
    """Model for tags result."""
    matched_tags: List[str] = Field(default_factory=list, description="Matched tags from tag pool")
    concept_tags: List[str] = Field(default_factory=list, description="Concept/keyword tags")
    person_org_tags: List[str] = Field(default_factory=list, description="Person and organization tags")
    time_place_tags: List[str] = Field(default_factory=list, description="Time and place tags")


class ArticleResult(BaseModel):
    """Model for individual item result."""
    index: int = Field(..., description="Item index")
    key: str = Field(..., description="Item key/ID")
    title: str = Field(..., description="Item title")
    tags: TagsResult = Field(..., description="Generated tags")

class AllTagsParams(BaseModel):
    """Model for all_tags endpoint."""
    collection: Optional[str] = Field(default="default_collection", description="Collection to retrieve tags from")

class AllTagsResponse(BaseModel):
    """Response model for all_tags endpoint."""
    collection: str = Field(..., description="Collection name")
    tags: List[dict] = Field(..., description="List of tags in the collection")


class HealthResponse(BaseModel):
    """Response model for health check endpoint."""
    status: str = Field(..., description="API status")


class ErrorResponse(BaseModel):
    """Response model for errors."""
    error: str = Field(..., description="Error message")
    details: Optional[str] = Field(None, description="Error details")
