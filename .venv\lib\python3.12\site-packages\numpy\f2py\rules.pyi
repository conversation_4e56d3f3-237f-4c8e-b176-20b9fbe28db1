from collections.abc import Callable, Iterable, Mapping
from typing import Any, Final, Type<PERSON><PERSON><PERSON>
from typing import Literal as L

from typing_extensions import TypeVar

from .__version__ import version
from .auxfuncs import _Bool, _Var

###

_VT = TypeVar("_VT", default=str)

_Predicate: TypeAlias = Callable[[_Var], _Bool]
_RuleDict: TypeAlias = dict[str, _VT]
_DefDict: TypeAlias = dict[_Predicate, _VT]

###

f2py_version: Final = version
numpy_version: Final = version

options: Final[dict[str, bool]] = ...
sepdict: Final[dict[str, str]] = ...

generationtime: Final[int] = ...
typedef_need_dict: Final[_DefDict[str]] = ...

module_rules: Final[_RuleDict[str | list[str] | _RuleDict]] = ...
routine_rules: Final[_RuleDict[str | list[str] | _DefDict | _RuleDict]] = ...
defmod_rules: Final[list[_RuleDict[str | _DefDict]]] = ...
rout_rules: Final[list[_RuleDict[str | Any]]] = ...
aux_rules: Final[list[_RuleDict[str | Any]]] = ...
arg_rules: Final[list[_RuleDict[str | Any]]] = ...
check_rules: Final[list[_RuleDict[str | Any]]] = ...

stnd: Final[dict[L[1, 2, 3, 4, 5, 6, 7, 8, 9, 0], L["st", "nd", "rd", "th"]]] = ...

def buildmodule(m: Mapping[str, str | Any], um: Iterable[Mapping[str, str | Any]]) -> _RuleDict: ...
def buildapi(rout: Mapping[str, str]) -> tuple[_RuleDict, str]: ...

# namespace pollution
k: str
