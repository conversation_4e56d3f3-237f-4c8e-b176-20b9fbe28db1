import pandas as pd
import spacy
import pytextrank
from typing import List, Tuple
import os
import time
from pydantic import BaseModel

from .gpt_structured import GPTResponse
from .embeddings_management import EmbeddingManager
from .csv_converter import csv_to_json


# Pipeline 1: textrank + sentence transformer (runs locally without using online API)
def extract_keywords_textrank(nlp_spacy_keywords, title="", abstract="", top_n=20):
    '''Process the title and abstract using spaCy with PyTextRank'''

    if "textrank" not in nlp_spacy_keywords.pipe_names:
        nlp_spacy_keywords.add_pipe("textrank")

    title_doc = nlp_spacy_keywords(title) if title.strip() else None
    abstract_doc = nlp_spacy_keywords(abstract) if abstract.strip() else None
    
    # Extract top-ranked phrases from the title
    abstract_keywords = title_keywords = []
    if title_doc:
        title_keywords = [phrase.text for phrase in title_doc._.phrases[:top_n]]
    if abstract_doc:
        # Extract top-ranked phrases from the abstract
        abstract_keywords = [phrase.text for phrase in abstract_doc._.phrases[:top_n]]
    if (title_keywords == []) and (abstract_keywords == []):
        return []
    
    # Combine title words with abstract keywords, prioritize title words
    keywords = title_keywords + abstract_keywords
    unique_keywords = list(dict.fromkeys(keywords))  # Remove duplicates while preserving order
    
    return unique_keywords[:top_n]

def get_entity_info_spacy(nlp_date_location, title: str, abstract: str = "", debug: bool = False) -> Tuple[List[str], List[str]]:
    '''Extract dates and date entities from the title and abstract using spaCy'''
    
    def extract_entities(text: str)  -> Tuple[List[str], List[str]]:
        doc = nlp_date_location(text)
        date_place_entities = [ent.text for ent in doc.ents if (ent.label_ in {"DATE", "GPE"}) and ((len(ent.text) < len(text) * 0.5) or (len(text.split()) < 8))]
        person_org_entities = [ent.text for ent in doc.ents if (ent.label_ in {"PERSON", "ORG"}) and ((len(ent.text) < len(text) * 0.5) or (len(text.split()) < 8))]
        return date_place_entities, person_org_entities

    # Extract entities from title and abstract
    title_dates_locations, title_persons_orgs = extract_entities(title)
    print("Title entities:", title_dates_locations, title_persons_orgs) if debug else None
    abstract_dates_locations, abstract_persons_orgs = extract_entities(abstract) if abstract.strip() else ([], [])
    print("Abstract entities:", abstract_dates_locations, abstract_persons_orgs) if debug else None
    
    # Combine and remove duplicates while preserving order
    combined_dates_locations = title_dates_locations + abstract_dates_locations
    unique_dates_locations = list(dict.fromkeys(combined_dates_locations))

    combined_persons_orgs = title_persons_orgs + abstract_persons_orgs
    unique_persons_orgs = list(dict.fromkeys(combined_persons_orgs))
    
    return unique_dates_locations, unique_persons_orgs

def labels_generator_textrank_st(embeddings_manager, nlp_spacy, article_title, article_abstract, collection_name="default_collection", search_by_category=False, debug=False):
    '''Textrank for extracting keywords from title and abstract and sentence transformer for matching between extracted keywords and labels in the label pool.'''
    print("Debug mode is ON. Printing info from attach_labels_textrank_st function:") if debug else None
    print("Article title:", article_title) if debug else None

    keywords_concept = extract_keywords_textrank(nlp_spacy, article_title, article_abstract, top_n=20)
    keywords_times_places, keywords_persons_organizations = get_entity_info_spacy(nlp_spacy, article_title, article_abstract, debug=debug)
    keywords_dict_from_spacy = {"keywords": keywords_concept, "persons_organizations": keywords_persons_organizations, "times_places": keywords_times_places}
    keywords_dict_from_spacy = {key: [s.capitalize() for s in value] for key, value in keywords_dict_from_spacy.items()}
    category_map= {"keywords": ["Concept"], "persons_organizations": ["Persons", "Institutions"], "times_places": ["Times and Places"]}
    matched_labels_dict = {
        keywords_type: (
            embeddings_manager.search(
                collection_name=collection_name,
                query_texts=keywords_list,
                top_k=2,
                simple_output=True,
                filter=f'category in {category_map[keywords_type]}' if search_by_category else None,
                search_params={"metric_type": "COSINE", "params": {"radius": 0.6}}
            )
            if keywords_list  # Only call search() if keywords_list is not empty
            else []  # Return an empty list if keywords_list is empty
        )
        for keywords_type, keywords_list in keywords_dict_from_spacy.items()
    }
    matched_labels_listofall = list(set([item for sublist in matched_labels_dict.values() for item in sublist]))
    keywords_dict_from_spacy["matched_labels"] = matched_labels_listofall

    print("Labels:", keywords_dict_from_spacy) if debug else None
    
    return keywords_dict_from_spacy

# Pipeline 2: LLM + sentence transformer (much better performance, needs access to API but runs at a very low cost)
def extract_keywords_gpt(title, abstract, model="gpt-4o-mini", top_n=20, retries=3, debug=False):
    """
    Extract keywords using GPT or other LLMs based on the title and abstract of the article.
    Retries up to three times if the response is None or invalid.

    Returns: A list of extracted keywords if successful, or None when refused or other errors occur.
    """

    # Construct the prompt based on whether abstract is provided
    if abstract is None:
        prompt_text = f"""Given the following academic article, identify the top {top_n} most relevant keywords, extract names of people and organizations, and guess any relevant time periods and places.\n\nArticle title: {title}."""
    elif title is None:
        prompt_text = f"""Given the following academic article, generate the top {top_n} most relevant keywords, extract names of people and organizations, and guess any relevant time periods and places.\n\nArticle abstract: {abstract}."""
    else:
        prompt_text = f"""Given the following academic article, generate the top {top_n} most relevant keywords, extract names of people and organizations, and guess any relevant time periods and places.\n\nArticle title: {title}\n\nArticle abstract: {abstract}."""
    
    class KeywordsExtraction(BaseModel):
        keywords: list[str]
        persons_organizations: list[str]
        times_places: list[str]
    
    # Get the GPT response
    gpt = GPTResponse()
    response = gpt.get_response(prompt_text, data_structure_class=KeywordsExtraction, model=model, debug=debug)

    if response is not None:
        return response.model_dump()
    elif retries > 1:
        print(f"Retrying... Attempts remaining: {retries - 1}")
        return extract_keywords_gpt(title, abstract, model, top_n, retries - 1, debug=debug)
    else:
        print("No luck after several attempts.")
        return None

def finalize_labels_gpt(title, abstract, matched_labels, model, debug):
    '''Using GPT-4o or other LLMs to generate the final labels based on the filtered labels. The unrelated labels are removed by GPT.'''
    
    if abstract is None:
        prompt_text = f"""Given the following academic article and label pool, remove irrelevant labels from the pool, sort the rest by relevance, and return the result:\n\nArticle title: {title}\n\nLabel pool: {'#'.join(matched_labels)}."""
    elif title is None:
        prompt_text = f"""Given the following academic article and label pool, remove irrelevant labels from the pool, sort the rest by relevance, and return the result:\n\nArticle abstract: {abstract}\n\nLabel pool: {'#'.join(matched_labels)}."""
    else:
        prompt_text = f"""Given the following academic article and label pool, remove irrelevant labels from the pool, sort the rest by relevance, and return the result:\n\nArticle title: {title}\n\nArticle abstract: {abstract}\n\nLabel pool: {'#'.join(matched_labels)}."""

    class KeywordsFinalization(BaseModel):
        keywords: list[str]

    gpt_finalizer = GPTResponse()
    response = gpt_finalizer.get_response(prompt_text, data_structure_class=KeywordsFinalization, model=model, debug=debug)
    if response is not None:
        return response.keywords
    else:
        print("Model refused to generate keywords or some other error occurred. Falling back to the matched keywords.")
        return matched_labels


def labels_generator_llm_st(embeddings_manager, label_pool_name_set, article_title, article_abstract, collection_name="default_collection", llm_model_name="gpt-4o-mini", debug=False):
    '''LLM for extracting keywords from title and abstract (Step 1) and finalizing the labels (Step 3). Sentence transformer for matching between extracted keywords and labels in the label pool (Step 2).'''
    
    print(f"Debug mode is ON. Printing info from labels_generator_llm_st function:\nArticle title:{article_title}\nArticle abstract:{article_abstract}\n") if debug else None

    # Step 1. generate keywords by LLM
    start = time.perf_counter() if debug else None
    keywords_dict_from_gpt = extract_keywords_gpt(article_title, article_abstract, model=llm_model_name, top_n=20, debug=False)
    if keywords_dict_from_gpt is None:
        return None
    end = time.perf_counter() if debug else None
    print(f"Code Block Time for keywords_from_gpt in labels_generator_llm_st: {end - start:.4f} seconds.\n") if debug else None
    print(f"Keywords from GPT:\n{keywords_dict_from_gpt}\n") if debug else None
    
    # Step 2. match generated keywords to labels in a label pool
    start = time.perf_counter() if debug else None
    # prematched_labels_list, keywords_dict_from_gpt_reduced = match_and_filter_keywords(keywords_dict_from_gpt, df_label_pool)
    # print(f"Prematched Labels:{prematched_labels_list}\n") if debug else None
    keywords_dict_from_gpt = {key: [s.capitalize() for s in value] for key, value in keywords_dict_from_gpt.items()}
    print(f"Keywords from GPT (capitalized):\n{keywords_dict_from_gpt}\n") if debug else None
    category_map= {"keywords": ["Concept"], "persons_organizations": ["Persons", "Institutions"], "times_places": ["Times and Places"]}
    matched_labels_dict = {
        keywords_type: embeddings_manager.search(
            collection_name=collection_name,
            query_texts=keywords_list,
            top_k=2,
            simple_output=True,
            filter=f'category in {category_map[keywords_type]}',
            search_params={"metric_type": "COSINE", "params": {"radius": 0.6}}
        ) if keywords_list else []
        for keywords_type, keywords_list in keywords_dict_from_gpt.items()
    }
    print("Matched Labels:\n", matched_labels_dict) if debug else None
    end = time.perf_counter() if debug else None
    print(f"Code Block Time for matched_labels in labels_generator_llm_st: {end - start:.4f} seconds") if debug else None

    # Step 3. LLM is used again to finalize the labels. Sanitization is done to prevent LLM-produced strange labels from entering the final result.
    start = time.perf_counter() if debug else None
    matched_labels_listofall = list(set([item for sublist in matched_labels_dict.values() for item in sublist]))
    finalized_matched_labels = finalize_labels_gpt(article_title, article_abstract, matched_labels_listofall, model=llm_model_name, debug=debug)

    ## Sanitizing strange labels from LLM finalization
    strange_finalized_labels = [label for label in finalized_matched_labels if label not in label_pool_name_set]
    if len(strange_finalized_labels) > 0:
        finalized_matched_labels_reduced = [label for label in finalized_matched_labels if label not in strange_finalized_labels]
        sanitized_strange_finalized_labels = embeddings_manager.search(
                collection_name=collection_name,
                query_texts=strange_finalized_labels,
                top_k=1,
                simple_output=True,
                search_params={"metric_type": "COSINE", "params": {"radius": 0.8}}
            )
        finalized_matched_labels = finalized_matched_labels_reduced + sanitized_strange_finalized_labels

    print(f"Finalized Matched Labels (sanitized):\n{finalized_matched_labels}\n") if debug else None
    keywords_dict_from_gpt["matched_labels"] = finalized_matched_labels
    end = time.perf_counter() if debug else None
    print(f"Code Block Time for finalization: {end - start:.4f} seconds") if debug else None

    return keywords_dict_from_gpt


if __name__ == "__main__":
    pipeline = 1
    llm_model_name = "gpt-4o-mini"
    embedding_model_name = "all-mpnet-base-v2"
    nlp_spacy_name = "en_core_web_md"
    # label_pool_path = './'
    collection_name = "default_collection"

    # https://data.isiscb.org/isis/citation/CBB772266181/
    article_title = "The Tenpō-Era (1830–1844) Map of Matsumae-no-shima and the Institutionalization of Tokugawa Cartography"
    article_abstract = """Japan’s early modern Tokugawa government (1603−1868) sponsored a series of projects of national mapping. The Matsumae family, ruling what is now Hokkaido, were loosely incorporated into these projects. It was only during the last of these, in the Tenpō era (1830−1848), that their lands were represented in the same manner as the rest of Japan because the central government made the final Matsumae-no-shima map. This article examines the production of this final official map of Japan’s north to argue that the Tokugawa’s institutional mapping made this region part of the nation through its own mapping framework, distinct from the cartographic forms with which national or imperial states are usually associated."""

    start = time.perf_counter()

    embeddings_manager = EmbeddingManager(embedding_model=embedding_model_name)
    nlp_spacy = spacy.load(nlp_spacy_name)

    current_directory = os.path.dirname(os.path.abspath(__file__))
    label_pool_path = os.path.join(current_directory, 'default_label_pool.csv')
    has_header = 'yes'
    json_data = csv_to_json(label_pool_path, delimiter='#', has_header=has_header)
    # df_data = pd.read_csv(label_pool_path, sep="#")
    # df_data["name"] = df_data["Name"].str.lower()

    # Create a collection and upsert data
    res = embeddings_manager.create_collection(collection_name=collection_name)
    if res == "success":
        embeddings_manager.upsert_data(collection_name=collection_name, data=json_data)
    embeddings_manager.load_collection(collection_name=collection_name, load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True)
    end = time.perf_counter()
    print(f"Time for creating and loading Milvus collection: {end - start:.4f} seconds")
    
    start = time.perf_counter()
    print(f"Start to generate labels using Pipeline {pipeline}...")
    results_dict={}
    if pipeline == 1:
        results_dict = labels_generator_textrank_st(
            embeddings_manager,
            nlp_spacy,
            collection_name=collection_name,
            article_title=article_title, 
            article_abstract=article_abstract,
            debug=False
        )
    elif pipeline == 2:
        results_dict = labels_generator_llm_st(
            embeddings_manager,
            collection_name=collection_name,
            article_title=article_title, 
            article_abstract=article_abstract, 
            llm_model_name=llm_model_name,
            debug=False
        )
    print(results_dict)
    end = time.perf_counter()
    print(f"Time for generating labels: {end - start:.4f} seconds")

    # print(f"Label attached using Pipeline {pipeline}:\n Final result: {matched_labels}\n Extracted concepts: {keywords_concepts}\n Extracted persons and organizations: {keywords_persons_organizations}\n Extracted times and places: {keywords_times_places}")
    # print(labels_generator_ori(article_title, article_abstract, pipeline=2))