from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Dict, Any
import asyncio

from config import ProductionSettings
from app.core.nlp_models import init_models
from app.api.routes import router as api_router


# Global variables to store shared resources
app_state: Dict[str, Any] = {}
settings = ProductionSettings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle - startup and shutdown events."""
    # Startup
    print("Starting FastAPI application...")

    # Initialize models and store in app state
    # Run model initialization in thread pool since it's CPU-bound
    embeddings_manager, nlp_spacy, label_pool_data_dict, label_pool_name_dict = await asyncio.to_thread(
        init_models, settings
    )

    app_state["embeddings_manager"] = embeddings_manager
    app_state["nlp_spacy"] = nlp_spacy
    app_state["label_pool_data_dict"] = label_pool_data_dict
    app_state["label_pool_name_dict"] = label_pool_name_dict

    print("Models initialized successfully")

    yield

    # Shutdown
    print("Shutting down FastAPI application...")
    # Clean up resources if needed
    app_state.clear()


def create_app() -> FastAPI:
    """Application factory function."""

    app = FastAPI(
        title=settings.api_title,
        description=settings.api_description,
        version=settings.api_version,
        lifespan=lifespan
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    app.include_router(api_router, prefix="/api")

    return app


# Create the FastAPI app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=5000, reload=False)
