import spacy
import os
import time
import pandas as pd
from .embeddings_management import Embedding<PERSON>ana<PERSON>
from .csv_converter import csv_to_json


def init_models(settings):
    """Initialize language models and load collections from the vector database.

    Args:
        settings: Pydantic Settings object for FastAPI
    """
    start = time.perf_counter()

    # Extract settings from Pydantic settings object
    st_model_name = settings.st_model_name
    nlp_spacy_name = settings.nlp_spacy_name
    milvus_uri = settings.milvus_uri
    collections_to_load = settings.collections_to_load
    openai_api_key = settings.openai_api_key

    # Load Sentence Transformer model and Spacy NLP model
    current_directory = os.path.dirname(os.path.abspath(__file__))
    embeddings_manager = EmbeddingManager(milvus_uri=milvus_uri, embedding_model=st_model_name, openai_api_key=openai_api_key)
    nlp_spacy = spacy.load(nlp_spacy_name)
    end = time.perf_counter()
    print(f"Time for initializing language models: {end - start:.4f} seconds")

    start = time.perf_counter()

    # For each collection to be loaded, create a collection if it doesn't exist, read data from local file and upsert data if the collection was newly created, and load the collection into memory
    label_pool_name_dict = {}
    label_pool_data_dict = {}
    for collection_name in collections_to_load:
        try:
            print(f"Start processing {collection_name}...")
            label_pool_path = os.path.join(current_directory, collection_name + '.csv')
            if not os.path.exists(label_pool_path):
                raise FileNotFoundError(f"Label pool file not found at {label_pool_path}")
            label_pool_data_dict[collection_name] = csv_to_json(label_pool_path, delimiter='|', has_header='yes')
            label_pool_data_df = pd.DataFrame(label_pool_data_dict[collection_name])
            label_pool_name_dict[collection_name] = set(label_pool_data_df['name'])
            print(f"Read {len(label_pool_data_dict[collection_name])} records from {label_pool_path}")
            
            res = embeddings_manager.create_collection(collection_name=collection_name)
            if res == "success":
                print("Database and collection created. Started to compute and upsert embeddings.")
                print(f"Processing {collection_name}...")

                embeddings_manager.upsert_data(collection_name=collection_name, data=label_pool_data_dict[collection_name])
                print(f"Upserted {len(label_pool_data_dict[collection_name])} records into collection '{collection_name}'. Now loading the collection into memory.")
            elif res == "exists":
                print("Collection already exists. Loading it into memory.")
            embeddings_manager.load_collection(collection_name=collection_name, load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True)
            print(f"Loaded collection '{collection_name}' into memory.")

            # The label_pool_data_dict and label_pool_name_dict should be loaded from the database, not from the local file, in order to reflect any changes that may have been made to the collection since it was last loaded from the local file
            # label_pool_data_dict[collection_name] = embeddings_manager.query(collection_name=collection_name, filter="1 == 1", output_fields=["record_id", "name", "category", "related_citations_count"])
            # label_pool_name_dict[collection_name] = set([row['name'] for row in label_pool_data_dict[collection_name]])
            # Read label data from csv file and convert to json

        except FileNotFoundError as e:
            print(f"Warning: {e}. Skipping this collection.")
            continue
    print("Label pool and its embeddings initialized.")

    end = time.perf_counter()
    print(f"Time for initialization: {end - start:.4f} seconds")

    return embeddings_manager, nlp_spacy, label_pool_data_dict, label_pool_name_dict