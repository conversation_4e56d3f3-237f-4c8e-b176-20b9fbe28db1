import os
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Base settings using Pydantic BaseSettings for FastAPI."""

    # API configuration
    api_title: str = "Bibliographic Items Tagging API"
    api_description: str = "API for automatic tagging of bibliographic items using ML-based models"
    api_version: str = "0.2.0"
    
    # Application paths
    app_root: str = os.path.dirname(os.path.abspath(__file__))
    core_dir: str = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'core')

    # Model configuration
    st_model_name: str = "all-mpnet-base-v2"
    nlp_spacy_name: str = "en_core_web_md"

    # Environment configuration
    environment: str = "production"
    debug: bool = False
    testing: bool = False

    # Vector database configuration
    collections_to_load: list[str] = ["default_collection", "full_collection"]
    milvus_uri: str = "./app/core/milvus.db"

    # CORS configuration
    cors_origins: list = ["http://localhost:5173", "https://biblio-tagging-tool.vercel.app"]

    # OpenAI configuration
    openai_api_key: str = ""

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False)


class ProductionSettings(Settings):
    """Production settings."""
    environment: str = "production"
    debug: bool = False
    testing: bool = False


class DevelopmentSettings(Settings):
    """Development settings."""
    environment: str = "development"
    debug: bool = True
    testing: bool = False
    collections_to_load: list[str] = ["default_collection"]


class TestingSettings(Settings):
    """Testing settings."""
    environment: str = "testing"
    debug: bool = True
    testing: bool = True
    collections_to_load: list[str] = ["test_collection"]
